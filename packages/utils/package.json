{"name": "@vben/utils", "version": "5.5.7", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/utils"}, "license": "MIT", "type": "module", "sideEffects": ["**/*.css"], "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}}, "dependencies": {"@types/lodash.defaultsdeep": "catalog:", "@vben-core/form-ui": "workspace:*", "@vben-core/shared": "workspace:*", "@vben-core/typings": "workspace:*", "bignumber.js": "catalog:", "lodash.defaultsdeep": "catalog:", "vue-router": "catalog:"}}