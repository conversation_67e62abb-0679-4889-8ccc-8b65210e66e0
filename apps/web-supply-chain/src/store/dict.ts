import { ref } from 'vue';

import { defineStore } from 'pinia';

import { getDictListApi } from '#/api';

export interface DictInfo {
  code: string;
  dictName: string;
  dictValue: string;
}

const localDictList: DictInfo[] = [];

export const useDictStore = defineStore(
  'dict',
  () => {
    const dictList = ref<DictInfo[]>([]);
    async function fetchDict() {
      const dictListRes = await getDictListApi();
      dictList.value = [...dictListRes, ...localDictList];
    }
    function getDictList(code: string) {
      return dictList.value.filter((item) => item.code === code);
    }
    function formatter(dictValue: string, code: string) {
      const dictList = getDictList(code);
      return (
        dictList.find((item) => item.dictValue === dictValue)?.dictName ||
        dictValue
      );
    }
    function $reset() {}
    return { $reset, dictList, fetchDict, getDictList, formatter };
  },
  {
    persist: {
      // 持久化
      pick: ['dictList'],
    },
  },
);
