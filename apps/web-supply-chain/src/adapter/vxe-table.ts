import { h } from 'vue';

import { $t } from '@vben/locales';
import { setupVbenVxeTable, useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';
import { formatDateMinute } from '@vben/utils';

import { Button, Image } from 'ant-design-vue';

import HelpModal from '#/components/help-modal.vue';
import StatusTag from '#/components/status-tag.vue';

import { useVbenForm } from './form';

const dictStore = useDictStore();

setupVbenVxeTable({
  configVxeTable: (vxeUI) => {
    vxeUI.setConfig({
      grid: {
        align: 'left',
        border: false,
        columnConfig: {
          minWidth: 'auto',
          resizable: true,
        },
        minHeight: 176,
        formConfig: {
          // 全局禁用vxe-table的表单配置，使用formOptions
          enabled: false,
        },
        proxyConfig: {
          autoLoad: true,
          response: {
            result: 'items',
            total: 'total',
            list: 'items',
          },
          showActiveMsg: true,
          showResponseMsg: false,
        },
        toolbarConfig: {
          custom: true,
          refresh: true,
          resizable: true,
          zoom: true,
        },
        round: true,
        showOverflow: true,
        size: 'small',
      },
    });

    // 表格配置项可以用 cellRender: { name: 'CellImage' },
    vxeUI.renderer.add('CellImage', {
      renderTableDefault(_renderOpts, params) {
        const { column, row } = params;
        return h(Image, { src: row[column.field] });
      },
    });

    // 表格配置项可以用 cellRender: { name: 'CellLink' },
    vxeUI.renderer.add('CellLink', {
      renderTableDefault(renderOpts) {
        const { props } = renderOpts;
        return h(Button, { size: 'small', type: 'link' }, { default: () => props?.text });
      },
    });
    vxeUI.renderer.add('ToolbarToolHelp', {
      renderToolbarTool(renderOpts) {
        const { props } = renderOpts;
        return h(HelpModal, props as { code: string });
      },
    });
    vxeUI.renderer.add('CellStatus', {
      renderTableDefault(renderOpts, params) {
        const { props } = renderOpts;
        const { $grid, row, column } = params;
        let color = '';
        if (props?.colorConfig) {
          for (const key in props?.colorConfig) {
            if (props?.colorConfig[key].includes(row[column.field])) {
              color = key;
            }
          }
        }
        let tooltipProps = {};
        if (props?.setTooltipShow && props?.setTooltipShow(row[column.field], params)) {
          tooltipProps = {
            title: row[props?.tooltipTitleKey] || props?.tooltipTitle,
            ...props?.tooltipProps,
          };
        }
        const iconShow = (props?.setTagIconShow && props?.setTagIconShow(row[column.field], params)) ?? false;
        return h(StatusTag, {
          color,
          cellLabel: $grid?.getCellLabel(row, column),
          iconShow,
          tooltipProps,
        });
      },
    });

    // 这里可以自行扩展 vxe-table 的全局配置，比如自定义格式化
    vxeUI.formats.add('formatStatus', {
      tableCellFormatMethod({ cellValue }, code: string) {
        return dictStore.formatter(cellValue, code) as string;
      },
    });
    vxeUI.formats.add('formatDateMinute', {
      cellFormatMethod({ cellValue }) {
        return formatDateMinute(cellValue);
      },
    });
    vxeUI.formats.add('formatBoolean', {
      tableCellFormatMethod({ cellValue }) {
        return cellValue === 1 ? $t('base.yes') : $t('base.no');
      },
    });
  },
  useVbenForm,
});

export { useVbenVxeGrid };

export type * from '@vben/plugins/vxe-table';
