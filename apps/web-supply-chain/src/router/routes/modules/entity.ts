import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-business',
      title: $t('page.entity.manage'),
    },
    name: 'Entity',
    path: '/entity',
    redirect: '/entity/enterprise',
    children: [
      {
        name: 'Enterprise',
        path: 'enterprise',
        component: () => import('#/views/entity/enterprise/index.vue'),
        meta: {
          icon: 'ic:baseline-business',
          title: $t('page.entity.enterprise'),
        },
      },
      {
        name: 'EntityEnterpriseEdit',
        path: 'enterprise/edit',
        component: () => import('#/views/entity/enterprise/edit.vue'),
        meta: {
          title: $t('page.entity.enterpriseEdit'),
          activePath: '/entity/enterprise',
          hideInMenu: true,
        },
      },
    ],
  },
];
export default routes;
