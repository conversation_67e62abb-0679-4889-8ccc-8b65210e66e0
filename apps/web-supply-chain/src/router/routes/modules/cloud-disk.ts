import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ri:file-cloud-line',
      title: $t('page.cloudDisk.group'),
    },
    name: 'CloudDisk',
    path: '/cloud-disk',
    children: [
      {
        name: 'Man<PERSON>',
        path: 'manage',
        component: () => import('#/views/cloud-disk/manage/index.vue'),
        meta: {
          icon: 'akar-icons:file',
          title: $t('page.cloudDisk.my'),
        },
      },
    ],
  },
];
export default routes;
