import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lets-icons:order',
      title: $t('page.order.group'),
    },
    name: 'Order',
    path: '/order',
    redirect: '/order/project',
    children: [
      {
        name: 'OrderProject',
        path: 'project',
        component: () => import('#/views/order/project/index.vue'),
        meta: {
          icon: 'ix:project',
          title: $t('page.order.project'),
        },
      },
      {
        name: 'OrderProjectAdd',
        path: 'project/add',
        component: () => import('#/views/order/project/edit.vue'),
        meta: {
          hideInMenu: true,
          activePath: '/order/project',
          icon: 'ix:project',
          title: $t('page.order.projectAdd'),
        },
      },
      {
        name: 'OrderProjectEdit',
        path: 'project/edit',
        component: () => import('#/views/order/project/edit.vue'),
        meta: {
          hideInMenu: true,
          activePath: '/order/project',
          icon: 'ix:project',
          title: $t('page.order.projectEdit'),
        },
      },
      {
        name: 'OrderProjectDetail',
        path: 'project/detail',
        component: () => import('#/views/order/project/detail.vue'),
        meta: {
          hideInMenu: true,
          activePath: '/order/project',
          icon: 'ix:project',
          title: $t('page.order.projectDetail'),
        },
      },
      {
        name: 'OrderBid',
        path: 'bid',
        component: () => import('#/views/order/bid/index.vue'),
        meta: {
          icon: 'ix:project',
          title: $t('page.order.bid'),
        },
      },
      {
        name: 'OrderBidAdd',
        path: 'bid/add',
        component: () => import('#/views/order/bid/edit.vue'),
        meta: {
          hideInMenu: true,
          activePath: '/order/bid',
          icon: 'ix:project',
          title: $t('page.order.bindAdd'),
        },
      },
      {
        name: 'OrderBidEdit',
        path: 'bid/edit',
        component: () => import('#/views/order/bid/edit.vue'),
        meta: {
          hideInMenu: true,
          activePath: '/order/bid',
          icon: 'ix:project',
          title: $t('page.order.bindEdit'),
        },
      },
      {
        name: 'OrderBidDetail',
        path: 'bid/detail',
        component: () => import('#/views/order/bid/detail.vue'),
        meta: {
          hideInMenu: true,
          activePath: '/order/bid',
          icon: 'ix:project',
          title: $t('page.order.bindDetail'),
        },
      },
      {
        name: 'OrderPurchase',
        path: 'purchase',
        component: () => import('#/views/order/purchase/index.vue'),
        meta: {
          icon: 'ix:project',
          title: $t('page.order.purchase'),
        },
      },
      {
        name: 'OrderPurchaseAdd',
        path: 'purchase/add',
        component: () => import('#/views/order/purchase/edit.vue'),
        meta: {
          hideInMenu: true,
          activePath: '/order/purchase',
          icon: 'ix:project',
          title: $t('page.order.purchaseAdd'),
        },
      },
      {
        name: 'OrderPurchaseEdit',
        path: 'purchase/edit',
        component: () => import('#/views/order/purchase/edit.vue'),
        meta: {
          hideInMenu: true,
          activePath: '/order/purchase',
          icon: 'ix:project',
          title: $t('page.order.purchaseEdit'),
        },
      },
      {
        name: 'OrderPurchaseDetail',
        path: 'purchase/detail',
        component: () => import('#/views/order/purchase/detail.vue'),
        meta: {
          hideInMenu: true,
          activePath: '/order/purchase',
          icon: 'ix:project',
          title: $t('page.order.purchaseDetail'),
        },
      },
    ],
  },
];

export default routes;
