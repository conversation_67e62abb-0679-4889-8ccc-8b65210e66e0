<script setup lang="ts">
import { Page } from '@vben/common-ui';
import { defineFormOptions } from '@vben/utils';

import { Button, Card } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import AttachmentList from '#/components/cloud-disk/attachment-list.vue';

const [BaseForm, baseFormApi] = useVbenForm(
  defineFormOptions({
    schema: [
      {
        component: 'Input',
        fieldName: 'name',
        label: '项目名称',
        rules: 'required',
      },
      {
        component: 'InputAmount',
        fieldName: 'amount',
        label: '项目金额',
        rules: 'required',
        componentProps: {
          inputProps: {
            suffix: '元',
          },
        },
      },
    ],
    showDefaultActions: false,
  }),
);
const save = async () => {
  const data = await baseFormApi.submitForm();
  console.log('save data:', data);
};
</script>

<template>
  <Page>
    <template #extra>
      <Button type="primary" @click="save">保存</Button>
    </template>
    <Card title="业务主体基本信息">
      <BaseForm />
    </Card>
    <AttachmentList class="mt-4" />
  </Page>
</template>

<style></style>
