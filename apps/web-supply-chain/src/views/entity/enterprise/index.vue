<script setup lang="ts">
import type { AppInfo } from '@vben/types';

import { useRouter } from 'vue-router';

import { Page, type VbenFormProps } from '@vben/common-ui';

import { Button, TabPane, Tabs } from 'ant-design-vue';

import { useVbenVxeGrid, type VxeTableGridOptions } from '#/adapter/vxe-table';
import { getEntityPageListApi } from '#/api/entity';

const formOptions: VbenFormProps = {
  schema: [
    {
      component: 'Input',
      fieldName: 'code',
      label: '统一社会信用代码',
      labelWidth: 120,
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: '业务主体全称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '主体状态',
      componentProps: {
        allowClear: true,
        showSearch: true,
        optionFilterProp: 'label',
        options: [
          {
            label: '待提交',
            value: '00',
          },
          {
            label: '已生效',
            value: '40',
          },
        ],
      },
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {},
};

const gridOptions: VxeTableGridOptions<AppInfo> = {
  columns: [
    { field: 'name', title: '业务主体全称' },
    { field: 'code', title: '统一社会信用代码' },
    { field: 'status', title: '主体状态' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getEntityPageListApi({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const router = useRouter();
const add = () => {
  router.push({ name: 'EntityEnterpriseEdit' });
};
</script>

<template>
  <Page auto-content-height>
    <Tabs type="card">
      <TabPane tab="企业信息列表">
        <Grid table-title="" table-title-help="提示">
          <template #toolbar-actions>
            <Button class="mr-2" type="primary" @click="add">新建</Button>
          </template>
        </Grid>
      </TabPane>
    </Tabs>
  </Page>
</template>

<style></style>
