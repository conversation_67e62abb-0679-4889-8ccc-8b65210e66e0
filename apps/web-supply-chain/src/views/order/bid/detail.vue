<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';

import { reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep, formatDate, formatMoney } from '@vben/utils';

import { Card } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getBidDetailApi } from '#/api';
import DynamicDescriptions from '#/components/dynamic-descriptions.vue';
import { useDictStore } from '@vben/stores';

const dictStore = useDictStore();
const bidData = ref({
  name: '',
  goodsList: [],
});
const baseFormSchema = [
  {
    fieldName: 'name',
    label: $t('order.bidding.bidName'),
  },
  {
    fieldName: 'code',
    label: $t('order.bidding.bidNumber'),
  },
  {
    fieldName: 'relatedType',
    label: $t('order.bidding.relatedType'),
    layoutType: 'tag',
    formatter: (dictValue: string) =>
      dictStore.formatter(dictValue, 'RELATED_TYPE'),
  },
  {
    fieldName: 'projectId',
    label: $t('order.project.projectName'),
    dependencies: {
      show(value) {
        return value.relatedType === '01';
      },
    },
  },
  {
    fieldName: 'projectCode',
    label: $t('order.project.projectCode'),
    dependencies: {
      show(value) {
        return value.relatedType === '01';
      },
    },
  },
  {
    fieldName: 'bindProgress',
    label: $t('order.bidding.bidProgress'),
    layoutType: 'tag',
    formatter: (dictValue: string) => {
      return dictStore.formatter(dictValue, 'BID_PROGRESS');
    },
  },
  {
    component: 'RadioGroup',
    fieldName: 'bindResult',
    label: $t('order.bidding.bidResult'),
    layoutType: 'tag',
    formatter: (dictValue: string) =>
      dictStore.formatter(dictValue, 'BID_RESULT'),
  },
];
const tenderFormSchema = [
  {
    fieldName: 'tenderName',
    label: $t('order.bidding.tenderName'),
  },
  {
    fieldName: 'tenderNumber',
    label: $t('order.bidding.tenderNumber'),
  },
  {
    fieldName: 'tenderEntity',
    label: $t('order.bidding.tenderEntity'),
  },
  {
    fieldName: 'tenderType',
    label: $t('order.bidding.tenderType'),
  },
  {
    fieldName: 'tenderPeriod',
    label: $t('order.bidding.tenderPeriod'),
    formatter: formatDate,
  },
  {
    fieldName: 'openingDate',
    label: $t('order.bidding.openingDate'),
    formatter: formatDate,
  },
  {
    fieldName: 'tenderContent',
    label: $t('order.bidding.tenderContent'),
    span: 3,
  },
];
const biddingFormSchema = [
  {
    fieldName: 'biddingInformation',
    label: $t('order.bidding.biddingInfo'),
  },
  {
    fieldName: 'biddingDate',
    label: $t('order.bidding.biddingDate'),
    formatter: formatDate,
  },
  {
    fieldName: 'biddingBudget',
    label: $t('order.bidding.biddingBudget'),
    formatter: formatMoney,
  },
  {
    fieldName: 'biddingDeposit',
    label: $t('order.bidding.biddingDeposit'),
    formatter: formatMoney,
  },
  {
    fieldName: 'biddingDocumentFee',
    label: $t('order.bidding.biddingDocumentFee'),
    span: 2,
    formatter: formatMoney,
  },
  {
    fieldName: 'competitorAnalysis',
    label: $t('order.bidding.competitorAnalysis'),
    span: 3,
  },
  {
    fieldName: 'remarks',
    label: $t('base.remark'),
    span: 3,
  },
];
const route = useRoute();
const totalData = reactive({
  goodsName: $t('base.total'),
  num: 0,
  amountTax: 0,
});
const updateFootEvent = () => {
  // eslint-disable-next-line no-use-before-define
  const $grid = gridApi.grid;
  if ($grid) {
    const { visibleData } = $grid.getTableData();
    const tableData = cloneDeep(visibleData);
    const total = { goodsName: $t('base.total'), num: 0, amountTax: 0 };
    tableData?.forEach((item) => {
      total.num += Number(item.num || 0);
      total.amountTax += Number(item.amountTax || 0);
    });
    totalData.num = total.num.toFixed(2);
    totalData.amountTax = total.amountTax.toFixed(2);
  }
};
const gridOptions: VxeGridProps = {
  columns: [
    {
      field: 'goodsName',
      title: $t('goods.goodsName'),
      editRender: {
        name: 'input',
      },
    },
    {
      field: 'models',
      title: $t('goods.models'),
      editRender: { name: 'input' },
    },
    {
      field: 'taxRate',
      title: $t('goods.taxRate'),
      editRender: { name: 'input' },
    },
    {
      field: 'num',
      title: $t('goods.num'),
      editRender: {
        name: 'input',
        immediate: true,
        events: {
          change: updateFootEvent,
        },
      },
    },
    {
      field: 'unitPriceTax',
      title: $t('goods.unitPriceTax'),
      editRender: { name: 'input' },
    },
    {
      field: 'amountTax',
      title: $t('goods.amountTax'),
      editRender: {
        name: 'input',
        immediate: true,
        events: {
          change: updateFootEvent,
        },
      },
    },
    {
      field: 'remark',
      title: $t('base.remark'),
      editRender: { name: 'input' },
    },
  ],
  showFooter: true,
  footerData: [totalData],
  border: 'inner',
  editRules: {
    goodsName: [{ required: true, content: $t('goods.rules.goodsName') }],
    taxRate: [{ required: true, content: $t('goods.rules.taxRate') }],
  },
  validConfig: {
    theme: 'normal',
  },
  pagerConfig: {
    enabled: false,
  },
  columnConfig: {
    minWidth: '',
  },
  toolbarConfig: {
    enabled: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
const getDetail = async () => {
  bidData.value = await getBidDetailApi({
    pid: route.query.pid as string,
  });
  await gridApi.grid.loadData(bidData.value.goodsList);
  updateFootEvent();
};
getDetail();
</script>

<template>
  <Page :title="bidData.name || '投标'" auto-content-height>
    <Card :title="$t('order.project.baseInfo')">
      <DynamicDescriptions :data="bidData" :schema="baseFormSchema" />
    </Card>
    <Card :title="$t('order.bidding.tenderInfo')" class="mt-4">
      <DynamicDescriptions :data="bidData" :schema="tenderFormSchema" />
    </Card>
    <Card :title="$t('order.bidding.biddingInfo')" class="mt-4">
      <DynamicDescriptions :data="bidData" :schema="biddingFormSchema" />
    </Card>
    <Card :title="$t('order.bidding.quoteInfo')" class="mt-4">
      <Grid />
    </Card>
  </Page>
</template>

<style></style>
