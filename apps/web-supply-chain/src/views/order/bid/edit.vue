<script setup lang="ts">
import type { EntityInfo } from '@vben/types';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { ProjectInfo } from '#/api';

import { reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep, defineFormOptions } from '@vben/utils';

import { Button, Card, Popconfirm, TypographyLink } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getProjectListApi } from '#/api';
import { getEntityListApi } from '#/api/entity';

const dictStore = useDictStore();

const bidData = ref({
  name: '',
  goodsList: [],
});
const [BaseForm, baseFormApi] = useVbenForm(
  defineFormOptions({
    schema: [
      {
        component: 'Input',
        fieldName: 'name',
        label: $t('order.bidding.bidName'),
        rules: 'required',
      },
      {
        component: 'TypographyText',
        fieldName: 'code',
        label: $t('order.bidding.bidNumber'),
      },
      {
        component: 'RadioGroup',
        fieldName: 'relatedType',
        label: $t('order.bidding.relatedType'),
        rules: 'required',
        componentProps: {
          options: [{ value: '1', label: '无关联' }, ...dictStore.getDictList('RELATED_TYPE')],
        },
      },
      {
        component: 'ApiSelect',
        fieldName: 'projectId',
        label: $t('order.project.projectName'),
        rules: 'required',
        componentProps: {
          labelInValue: true,
          api: getProjectListApi,
          afterFetch(data: ProjectInfo[]) {
            return data.map((item) => ({
              label: item.name,
              value: item.pid,
              code: item.code,
            }));
          },
          onChange(value: { option: { code: string } }) {
            baseFormApi.setFieldValue('projectCode', value.option.code);
          },
        },
        dependencies: {
          show(value) {
            return value.relatedType === '01';
          },
          triggerFields: ['relatedType'],
        },
      },
      {
        component: 'TypographyText',
        fieldName: 'projectCode',
        label: $t('order.project.projectCode'),
        dependencies: {
          show(value) {
            return value.relatedType === '01';
          },
          triggerFields: ['relatedType'],
        },
      },
      {
        component: 'RadioGroup',
        fieldName: 'bindProgress',
        label: $t('order.bidding.bidProgress'),
        componentProps: {
          options: dictStore.getDictList('BID_PROGRESS'),
        },
        rules: 'required',
      },
      {
        component: 'RadioGroup',
        fieldName: 'bindResult',
        label: $t('order.bidding.bidResult'),
        componentProps: {
          options: dictStore.getDictList('BID_RESULT'),
        },
        rules: 'required',
      },
    ],
    showDefaultActions: false,
  }),
);
const [TenderForm, TenderFormApi] = useVbenForm(
  defineFormOptions({
    schema: [
      {
        component: 'Input',
        fieldName: 'tenderName',
        label: $t('order.bidding.tenderName'),
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'tenderNumber',
        label: $t('order.bidding.tenderNumber'),
      },
      {
        component: 'ApiSelect',
        fieldName: 'tenderEntity',
        label: $t('order.bidding.tenderEntity'),
        rules: 'required',
        componentProps: {
          api: getEntityListApi,
          afterFetch(data: EntityInfo[]) {
            return data.map((item: EntityInfo) => ({
              label: item.name,
              value: item.pid,
            }));
          },
        },
      },
      {
        component: 'Input',
        fieldName: 'tenderType',
        label: $t('order.bidding.tenderType'),
      },
      {
        component: 'RangePicker',
        fieldName: 'tenderPeriod',
        label: $t('order.bidding.tenderPeriod'),
      },
      {
        component: 'DatePicker',
        fieldName: 'openingDate',
        label: $t('order.bidding.openingDate'),
      },
      {
        component: 'Textarea',
        fieldName: 'tenderContent',
        label: $t('order.bidding.tenderContent'),
        formItemClass: 'col-span-3',
        componentProps: {
          rows: 3,
        },
      },
    ],
    showDefaultActions: false,
  }),
);
const [BiddingForm, BiddingFormApi] = useVbenForm(
  defineFormOptions({
    schema: [
      {
        component: 'Input',
        fieldName: 'biddingInformation',
        label: $t('order.bidding.biddingInfo'),
      },
      {
        component: 'DatePicker',
        fieldName: 'biddingDate',
        label: $t('order.bidding.biddingDate'),
      },
      {
        component: 'InputAmount',
        fieldName: 'biddingBudget',
        label: $t('order.bidding.biddingBudget'),
      },
      {
        component: 'InputAmount',
        fieldName: 'biddingDeposit',
        label: $t('order.bidding.biddingDeposit'),
      },
      {
        component: 'InputAmount',
        fieldName: 'biddingDocumentFee',
        label: $t('order.bidding.biddingDocumentFee'),
      },
      {
        component: 'Textarea',
        fieldName: 'competitorAnalysis',
        label: $t('order.bidding.competitorAnalysis'),
        formItemClass: 'col-span-3',
        componentProps: {
          rows: 3,
        },
      },
      {
        component: 'Textarea',
        fieldName: 'remarks',
        label: $t('base.remark'),
        formItemClass: 'col-span-3',
        componentProps: {
          rows: 3,
        },
      },
    ],
    commonConfig: {
      labelWidth: 120,
    },
    showDefaultActions: false,
  }),
);
const totalData = reactive({
  goodsName: $t('base.total'),
  num: 0,
  amountTax: 0,
});
const updateFootEvent = () => {
  // eslint-disable-next-line no-use-before-define
  const $grid = gridApi.grid;
  if ($grid) {
    const { visibleData } = $grid.getTableData();
    const tableData = cloneDeep(visibleData);
    const total = { goodsName: $t('base.total'), num: 0, amountTax: 0 };
    tableData?.forEach((item) => {
      total.num += Number(item.num || 0);
      total.amountTax += Number(item.amountTax || 0);
    });
    totalData.num = total.num;
    totalData.amountTax = total.amountTax;
  }
};
const gridOptions: VxeGridProps = {
  columns: [
    {
      field: 'goodsName',
      title: $t('goods.goodsName'),
      editRender: {
        name: 'input',
      },
    },
    {
      field: 'models',
      title: $t('goods.models'),
      editRender: { name: 'input' },
    },
    {
      field: 'taxRate',
      title: $t('goods.taxRate'),
      editRender: { name: 'input' },
    },
    {
      field: 'num',
      title: $t('goods.num'),
      editRender: {
        name: 'input',
        immediate: true,
        events: {
          change: updateFootEvent,
        },
      },
    },
    {
      field: 'unitPriceTax',
      title: $t('goods.unitPriceTax'),
      editRender: { name: 'input' },
    },
    {
      field: 'amountTax',
      title: $t('goods.amountTax'),
      editRender: {
        name: 'input',
        immediate: true,
        events: {
          change: updateFootEvent,
        },
      },
    },
    {
      field: 'remark',
      title: $t('base.remark'),
      editRender: { name: 'input' },
    },
    {
      field: 'action',
      title: $t('base.action'),
      fixed: 'right',
      slots: { default: 'action' },
    },
  ],
  showFooter: true,
  footerData: [totalData],
  border: 'inner',
  editRules: {
    goodsName: [{ required: true, content: $t('goods.rules.goodsName') }],
    taxRate: [{ required: true, content: $t('goods.rules.taxRate') }],
  },
  validConfig: {
    theme: 'normal',
  },
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  pagerConfig: {
    enabled: false,
  },
  columnConfig: {
    minWidth: '',
  },
  toolbarConfig: {
    enabled: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
const addGoods = async () => {
  const record = {};
  bidData.value.goodsList.push(record);
  const $grid = gridApi.grid;
  if ($grid) {
    const { row } = await $grid.insertAt(record, null);
    await $grid.setEditRow(row);
    updateFootEvent();
  }
};
const delGoods = async (row) => {
  const $grid = gridApi.grid;
  if ($grid) {
    await $grid.remove(row);
    updateFootEvent();
  }
};
const save = () => {};
</script>

<template>
  <Page :title="bidData.name || '投标新建'" auto-content-height>
    <template #extra>
      <Button type="primary" @click="save">{{ $t('base.save') }}</Button>
      <Button type="primary">{{ $t('base.submit') }}</Button>
    </template>
    <Card :title="$t('order.project.baseInfo')">
      <BaseForm />
    </Card>
    <Card :title="$t('order.bidding.tenderInfo')" class="mt-4">
      <TenderForm />
    </Card>
    <Card :title="$t('order.bidding.biddingInfo')" class="mt-4">
      <BiddingForm />
    </Card>
    <Card :title="$t('order.bidding.quoteInfo')" class="mt-4">
      <Grid>
        <template #action="{ row }">
          <Popconfirm :title="$t('base.confirmDel')" @confirm="delGoods(row)">
            <TypographyLink href="javascript:">
              {{ $t('base.del') }}
            </TypographyLink>
          </Popconfirm>
        </template>
        <template #bottom>
          <Button block class="mt-2" type="dashed" @click="addGoods()">
            {{ $t('base.addNewLine') }}
          </Button>
        </template>
      </Grid>
    </Card>
  </Page>
</template>

<style></style>
