<script setup lang="ts">
import type { EntityInfo } from '@vben/types';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep, defineFormOptions } from '@vben/utils';

import { Button, Card, Popconfirm, TypographyLink } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getEntityListApi } from '#/api/entity';
import { useDictStore } from '@vben/stores';

const dictStore = useDictStore();
const projectData = ref({
  goodsList: [],
});
const [BaseForm, baseFormApi] = useVbenForm(
  defineFormOptions({
    schema: [
      {
        component: 'Input',
        fieldName: 'name',
        label: $t('order.project.projectName'),
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'code',
        label: $t('order.project.projectCode'),
        componentProps: {
          disabled: true,
        },
      },
      {
        component: 'Select',
        fieldName: 'mode',
        label: $t('order.project.projectMode'),
        rules: 'required',
        componentProps: {
          options: dictStore.getDictList('PROJECT_MODE'),
        },
      },
      {
        component: 'Textarea',
        fieldName: 'remark',
        label: $t('base.remark'),
        formItemClass: 'col-span-3',
        componentProps: {
          rows: 3,
        },
      },
    ],
    showDefaultActions: false,
  }),
);
const [NodeForm, nodeFormApi] = useVbenForm(
  defineFormOptions({
    schema: [
      {
        component: 'CheckboxGroup',
        fieldName: 'nodeInfo',
        help: $t('order.project.nodeInfoHelp'),
        label: $t('order.project.nodeInfo'),
        formItemClass: 'col-span-3',
        componentProps: {
          options: dictStore.getDictList('NODE_TYPE'),
        },
      },
    ],
    showDefaultActions: false,
  }),
);
const [BusinessForm, businessFormApi] = useVbenForm(
  defineFormOptions({
    schema: [
      {
        component: 'ApiSelect',
        fieldName: 'tradeExecutionEnterprise',
        label: $t('base.tradeExecutionEnterprise'),
        rules: 'required',
        componentProps: {
          api: getEntityListApi,
          afterFetch(data: EntityInfo[]) {
            return data.map((item: EntityInfo) => ({
              label: item.name,
              value: item.pid,
            }));
          },
        },
      },
      {
        component: 'ApiSelect',
        fieldName: 'upEnterprise',
        label: $t('base.upstreamEnterprise'),
        componentProps: {
          api: getEntityListApi,
          afterFetch(data: EntityInfo[]) {
            return data.map((item: EntityInfo) => ({
              label: item.name,
              value: item.pid,
            }));
          },
        },
      },
      {
        component: 'ApiSelect',
        fieldName: 'downEnterprise',
        label: $t('base.downstreamEnterprise'),
        componentProps: {
          api: getEntityListApi,
          afterFetch(data: EntityInfo[]) {
            return data.map((item: EntityInfo) => ({
              label: item.name,
              value: item.pid,
            }));
          },
        },
      },
      {
        component: 'Select',
        fieldName: 'projectType',
        label: $t('order.project.projectType'),
        rules: 'required',
        componentProps: {
          options: dictStore.getDictList('ACCESS_STATE'),
        },
      },
      {
        component: 'RadioGroup',
        fieldName: 'deliveryMethod',
        label: $t('order.project.deliveryMethod'),
        rules: 'required',
        componentProps: {
          options: dictStore.getDictList('DELIVERY_METHOD'),
        },
      },
      {
        component: 'DatePicker',
        fieldName: 'businessDate',
        rules: 'required',
        label: $t('order.project.businessDate'),
      },
      {
        component: 'DatePicker',
        fieldName: 'planEndDate',
        label: $t('order.project.planEndDate'),
      },
      {
        component: 'Cascader',
        fieldName: 'projectAddress',
        label: $t('order.project.projectAddress'),
        componentProps: {
          options: [
            {
              value: 'beijing',
              label: '北京',
              children: [
                {
                  value: 'beijing',
                  label: '北京',
                  children: [
                    {
                      value: 'xichengqu',
                      label: '西城区',
                    },
                  ],
                },
              ],
            },
          ],
        },
      },
      {
        component: 'Input',
        fieldName: 'projectAddressDetail',
        label: $t('order.project.projectAddressDetail'),
      },
    ],
    showDefaultActions: false,
  }),
);
const [FinanceForm, financeFormApi] = useVbenForm(
  defineFormOptions({
    schema: [
      {
        component: 'RadioGroup',
        fieldName: 'hasFinance',
        label: $t('order.project.hasFinance'),
        rules: 'required',
        defaultValue: 0,
        componentProps: {
          options: [
            { label: '是', value: 1 },
            { label: '否', value: 0 },
          ],
        },
      },
      {
        component: 'RadioGroup',
        fieldName: 'hasGuarantee',
        label: $t('order.project.hasGuarantee'),
        rules: 'required',
        defaultValue: 0,
        componentProps: {
          options: [
            { label: '是', value: 1 },
            { label: '否', value: 0 },
          ],
        },
      },
      {
        component: 'ApiSelect',
        fieldName: 'fundingParty',
        label: $t('base.fundingParty'),
        componentProps: {
          api: getEntityListApi,
          afterFetch(data: EntityInfo[]) {
            return data.map((item: EntityInfo) => ({
              label: item.name,
              value: item.pid,
            }));
          },
        },
      },
      {
        component: 'ApiSelect',
        fieldName: 'guarantor',
        label: $t('base.guarantor'),
        componentProps: {
          api: getEntityListApi,
          afterFetch(data: EntityInfo[]) {
            return data.map((item: EntityInfo) => ({
              label: item.name,
              value: item.pid,
            }));
          },
        },
      },
      {
        component: 'DatePicker',
        fieldName: 'planStartDate',
        label: $t('order.project.planStartDate'),
      },
      {
        component: 'DatePicker',
        fieldName: 'planCompletionDate',
        label: $t('order.project.planCompletionDate'),
      },
      {
        component: 'Input',
        fieldName: 'planDays',
        label: $t('order.project.planDays'),
      },
      {
        component: 'Input',
        fieldName: 'planAmount',
        label: $t('order.project.planAmount'),
      },
    ],
    commonConfig: {
      labelWidth: 140,
    },
    showDefaultActions: false,
  }),
);
const totalData = reactive({
  goodsName: $t('base.total'),
  num: 0,
  amountTax: 0,
});
const updateFootEvent = () => {
  // eslint-disable-next-line no-use-before-define
  const $grid = gridApi.grid;
  if ($grid) {
    const { visibleData } = $grid.getTableData();
    const tableData = cloneDeep(visibleData);
    const total = { goodsName: $t('base.total'), num: 0, amountTax: 0 };
    tableData?.forEach((item) => {
      total.num += Number(item.num || 0);
      total.amountTax += Number(item.amountTax || 0);
    });
    totalData.num = total.num;
    totalData.amountTax = total.amountTax;
  }
};
const gridOptions: VxeGridProps = {
  columns: [
    {
      field: 'goodsName',
      title: $t('goods.goodsName'),
      editRender: {
        name: 'input',
      },
    },
    {
      field: 'models',
      title: $t('goods.models'),
      editRender: { name: 'input' },
    },
    {
      field: 'taxRate',
      title: $t('goods.taxRate'),
      editRender: { name: 'input' },
    },
    {
      field: 'num',
      title: $t('goods.num'),
      editRender: {
        name: 'input',
        immediate: true,
        events: {
          change: updateFootEvent,
        },
      },
    },
    {
      field: 'unitPriceTax',
      title: $t('goods.unitPriceTax'),
      editRender: { name: 'input' },
    },
    {
      field: 'amountTax',
      title: $t('goods.amountTax'),
      editRender: {
        name: 'input',
        immediate: true,
        events: {
          change: updateFootEvent,
        },
      },
    },
    {
      field: 'remark',
      title: $t('base.remark'),
      editRender: { name: 'input' },
    },
    {
      field: 'action',
      title: $t('base.action'),
      fixed: 'right',
      slots: { default: 'action' },
    },
  ],
  showFooter: true,
  footerData: [totalData],
  border: 'inner',
  editRules: {
    goodsName: [{ required: true, content: $t('goods.rules.goodsName') }],
    taxRate: [{ required: true, content: $t('goods.rules.taxRate') }],
  },
  validConfig: {
    theme: 'normal',
  },
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  pagerConfig: {
    enabled: false,
  },
  columnConfig: {
    minWidth: '',
  },
  toolbarConfig: {
    enabled: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
const addGoods = async () => {
  const record = {};
  projectData.value.goodsList.push(record);
  const $grid = gridApi.grid;
  if ($grid) {
    const { row } = await $grid.insertAt(record, null);
    await $grid.setEditRow(row);
    updateFootEvent();
  }
};
const delGoods = async (row) => {
  const $grid = gridApi.grid;
  if ($grid) {
    await $grid.remove(row);
    updateFootEvent();
  }
};
const save = async () => {
  const values = await baseFormApi
    .merge(nodeFormApi)
    .merge(businessFormApi)
    .merge(financeFormApi)
    .submitAllForm(true);
  console.log('values:', values);
  console.log(gridApi.grid.getFullData());
};
</script>

<template>
  <Page auto-content-height title="新建">
    <template #extra>
      <Button type="primary" @click="save">{{ $t('base.save') }}</Button>
      <Button type="primary">{{ $t('base.submit') }}</Button>
    </template>
    <Card :title="$t('order.project.baseInfo')">
      <BaseForm />
    </Card>
    <Card class="mt-4" :title="$t('order.project.nodeInfo')">
      <NodeForm />
    </Card>
    <Card class="mt-4" :title="$t('order.project.businessInfo')">
      <BusinessForm />
    </Card>
    <Card class="mt-4" :title="$t('order.project.financialInfo')">
      <FinanceForm />
    </Card>
    <Card class="mt-4" :title="$t('order.project.goodsInfo')">
      <Grid>
        <template #action="{ row }">
          <Popconfirm :title="$t('base.confirmDel')" @confirm="delGoods(row)">
            <TypographyLink href="javascript:">
              {{ $t('base.del') }}
            </TypographyLink>
          </Popconfirm>
        </template>
        <template #bottom>
          <Button block class="mt-2" type="dashed" @click="addGoods()">
            {{ $t('base.addNewLine') }}
          </Button>
        </template>
      </Grid>
    </Card>
  </Page>
</template>

<style></style>
