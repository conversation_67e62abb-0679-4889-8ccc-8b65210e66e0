<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { EntityInfo } from '@vben/types';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { Button } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getProjectPageListApi } from '#/api';
import { getEntityListApi } from '#/api/entity';
import { useDictStore } from '@vben/stores';

const dictStore = useDictStore();

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'code',
      label: $t('order.project.projectCode'),
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: $t('order.project.projectName'),
    },
    {
      component: 'Select',
      fieldName: 'auditStatus',
      label: $t('base.auditStatus'),
      componentProps: {
        options: dictStore.getDictList('ACCESS_STATE'),
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: $t('order.project.projectStatus'),
      componentProps: {
        options: dictStore.getDictList('ACCESS_STATE'),
      },
    },
    {
      component: 'Select',
      fieldName: 'mode',
      label: $t('order.project.projectMode'),
      componentProps: {
        options: dictStore.getDictList('ACCESS_STATE'),
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'tradeExecutionEnterprise',
      label: $t('base.tradeExecutionEnterprise'),
      componentProps: {
        api: getEntityListApi,
        afterFetch(data: EntityInfo[]) {
          return data.map((item: EntityInfo) => ({
            label: item.name,
            value: item.pid,
          }));
        },
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'upEnterprise',
      label: $t('base.upstreamEnterprise'),
      componentProps: {
        api: getEntityListApi,
        afterFetch(data: EntityInfo[]) {
          return data.map((item: EntityInfo) => ({
            label: item.name,
            value: item.pid,
          }));
        },
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'downEnterprise',
      label: $t('base.downstreamEnterprise'),
      componentProps: {
        api: getEntityListApi,
        afterFetch(data: EntityInfo[]) {
          return data.map((item: EntityInfo) => ({
            label: item.name,
            value: item.pid,
          }));
        },
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'businessDate',
      label: $t('order.project.businessDate'),
    },
    {
      component: 'Input',
      fieldName: 'createDepartment',
      label: $t('base.createDepartment'),
    },
    {
      component: 'Input',
      fieldName: 'createBy',
      label: $t('base.createdBy'),
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: $t('base.creationTime'),
      componentProps: {
        showTime: true,
      },
    },
  ],
  fieldMappingTime: [
    ['businessDate', ['businessDateStart', 'businessDateStartEnd'], 'x'],
    ['createTime', ['createTimeStart', 'createTimeEnd'], 'x'],
  ],
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: 60 },
    { field: 'code', title: $t('order.project.projectCode'), minWidth: 120 },
    { field: 'name', title: $t('order.project.projectName') },
    {
      field: 'auditStatus',
      title: $t('base.auditStatus'),
      formatter: ['formatStatus', 'AUDIT_STATUS'],
      width: 120,
      cellRender: {
        name: 'CellStatus',
        props: {
          tooltipTitleKey: 'reason',
          setTagIconShow: (cellValue: string) => {
            if (cellValue === '20') {
              return true;
            }
          },
          setTooltipShow: (cellValue: string) => {
            if (cellValue === '20') {
              return true;
            }
          },
          colorConfig: {
            success: ['10'],
            error: ['20'],
          },
        },
      },
    },
    {
      field: 'status',
      title: $t('base.status'),
      width: 120,
      formatter: ['formatStatus', 'PROJECT_STATUS'],
      cellRender: {
        name: 'CellStatus',
        props: {
          colorConfig: {
            processing: ['10'],
            success: ['20'],
            warning: ['30'],
            error: ['40'],
          },
        },
      },
    },
    {
      field: 'mode',
      title: $t('order.project.projectMode'),
      formatter: ['formatStatus', 'PROJECT_MODE'],
    },
    {
      field: 'tradeExecutionEnterpriseName',
      title: $t('base.tradeExecutionEnterprise'),
    },
    { field: 'upEnterpriseName', title: $t('base.upstreamEnterprise') },
    { field: 'downEnterpriseName', title: $t('base.downstreamEnterprise') },
    { field: 'projectType', title: $t('order.project.projectType') },
    {
      field: 'businessDate',
      title: $t('order.project.businessDate'),
      formatter: 'formatDate',
    },
    { field: 'createDepartment', title: $t('base.createDepartment') },
    { field: 'createBy', title: $t('base.createdBy') },
    {
      field: 'createTime',
      title: $t('base.creationTime'),
      formatter: 'formatDateMinute',
    },
    {
      field: 'action',
      title: $t('base.action'),
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getProjectPageListApi({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    tools: [
      { toolRender: { name: 'ToolbarToolHelp', props: { code: '123' } } },
    ],
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const router = useRouter();
const add = () => {
  router.push({ name: 'OrderProjectAdd' });
};
const edit = (row) => {
  router.push({ name: 'OrderProjectEdit', query: { id: row.id } });
};
const detail = (row) => {
  router.push({ name: 'OrderProjectDetail', query: { id: row.id } });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Button size="small" type="link" @click="edit(row)">
          {{ $t('base.edit') }}
        </Button>
        <Button size="small" type="link" @click="detail(row)">
          {{ $t('base.detail') }}
        </Button>
      </template>
    </Grid>
  </Page>
</template>

<style></style>
