<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';

import { reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep, formatDate, formatMoney } from '@vben/utils';

import { Card, TabPane, Tabs } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getProjectDetailApi } from '#/api';
import DynamicDescriptions from '#/components/dynamic-descriptions.vue';
import StatusTag from '#/components/status-tag.vue';
import { useDictStore } from '@vben/stores';

const dictStore = useDictStore();

const route = useRoute();
const activeKey = ref(['base', 'node', 'business', 'finance', 'goods']);
const projectDetail = ref<{ goodsList: []; name: string }>({
  goodsList: [],
  name: '',
});
const baseFormSchema = [
  {
    fieldName: 'name',
    label: $t('order.project.projectName'),
  },
  {
    fieldName: 'code',
    label: $t('order.project.projectCode'),
  },
  {
    fieldName: 'mode',
    label: $t('order.project.projectMode'),
    layoutType: 'tag',
    formatter: (dictValue: string) =>
      dictStore.formatter(dictValue, 'PROJECT_MODE'),
  },
  {
    fieldName: 'remark',
    label: $t('base.remark'),
    span: 3,
  },
  {
    fieldName: 'auditStatus',
    label: $t('base.auditStatus'),
    layoutType: 'tag',
    formatter: (dictValue: string) =>
      dictStore.formatter(dictValue, 'AUDIT_STATUS'),
  },
  {
    fieldName: 'status',
    label: $t('base.status'),
    layoutType: 'tag',
    formatter: (dictValue: string) =>
      dictStore.formatter(dictValue, 'PROJECT_STATUS'),
  },
];
const nodeFormSchema = [
  {
    fieldName: 'nodeInfo',
    label: $t('order.project.nodeInfo'),
    slotName: 'nodeInfo',
  },
];
const businessFormSchema = [
  {
    fieldName: 'tradeExecutionEnterpriseName',
    label: $t('base.tradeExecutionEnterprise'),
  },
  {
    fieldName: 'upEnterpriseName',
    label: $t('base.upstreamEnterprise'),
  },
  {
    fieldName: 'downEnterpriseName',
    label: $t('base.downstreamEnterprise'),
  },
  {
    fieldName: 'projectType',
    label: $t('order.project.projectType'),
  },
  {
    fieldName: 'deliveryMethod',
    label: $t('order.project.deliveryMethod'),
    layoutType: 'tag',
    formatter: (dictValue: string) =>
      dictStore.formatter(dictValue, 'DELIVERY_METHOD'),
  },
  {
    fieldName: 'businessDate',
    label: $t('order.project.businessDate'),
    formatter: formatDate,
  },
  {
    fieldName: 'planEndDate',
    label: $t('order.project.planEndDate'),
    formatter: formatDate,
  },
  {
    fieldName: 'projectAddressName',
    label: $t('order.project.projectAddress'),
  },
  {
    component: 'Input',
    fieldName: 'projectAddressDetail',
    label: $t('order.project.projectAddressDetail'),
  },
];
const financeFormSchema = [
  {
    fieldName: 'hasFinance',
    label: $t('order.project.hasFinance'),
    layoutType: 'tag',
    formatter(value: 0 | 1): string {
      return value === 1 ? $t('base.yes') : $t('base.no');
    },
  },
  {
    fieldName: 'hasGuarantee',
    label: $t('order.project.hasGuarantee'),
    layoutType: 'tag',
    formatter(value: 0 | 1): string {
      return value === 1 ? $t('base.yes') : $t('base.no');
    },
  },
  {
    fieldName: 'fundingPartyName',
    label: $t('base.fundingParty'),
  },
  {
    fieldName: 'guarantorName',
    label: $t('base.guarantor'),
  },
  {
    fieldName: 'planStartDate',
    label: $t('order.project.planStartDate'),
  },
  {
    fieldName: 'planCompletionDate',
    label: $t('order.project.planCompletionDate'),
  },
  {
    fieldName: 'planDays',
    label: $t('order.project.planDays'),
  },
  {
    fieldName: 'planAmount',
    label: $t('order.project.planAmount'),
    formatter: formatMoney,
  },
];
const totalData = reactive({
  goodsName: $t('base.total'),
  num: 0,
  amountTax: 0,
});
const updateFootEvent = () => {
  // eslint-disable-next-line no-use-before-define
  const $grid = gridApi.grid;
  if ($grid) {
    const { visibleData } = $grid.getTableData();
    const tableData = cloneDeep(visibleData);
    const total = { goodsName: $t('base.total'), num: 0, amountTax: 0 };
    tableData?.forEach((item) => {
      total.num += Number(item.num || 0);
      total.amountTax += Number(item.amountTax || 0);
    });
    totalData.num = total.num;
    totalData.amountTax = total.amountTax;
  }
};
const gridOptions: VxeGridProps = {
  columns: [
    { type: 'seq', width: 60 },
    {
      field: 'goodsName',
      title: $t('goods.goodsName'),
      editRender: {
        name: 'input',
      },
    },
    {
      field: 'models',
      title: $t('goods.models'),
      editRender: { name: 'input' },
    },
    {
      field: 'taxRate',
      title: $t('goods.taxRate'),
      editRender: { name: 'input' },
    },
    {
      field: 'num',
      title: $t('goods.num'),
      editRender: {
        name: 'input',
        immediate: true,
        events: {
          change: updateFootEvent,
        },
      },
    },
    {
      field: 'unitPriceTax',
      title: $t('goods.unitPriceTax'),
      editRender: { name: 'input' },
    },
    {
      field: 'amountTax',
      title: $t('goods.amountTax'),
      editRender: {
        name: 'input',
        immediate: true,
        events: {
          change: updateFootEvent,
        },
      },
    },
    {
      field: 'remark',
      title: $t('base.remark'),
      editRender: { name: 'input' },
    },
  ],
  showFooter: true,
  footerData: [totalData],
  border: 'inner',
  pagerConfig: {
    enabled: false,
  },
  columnConfig: {
    minWidth: '',
  },
  toolbarConfig: {
    enabled: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
const getDetail = async () => {
  projectDetail.value = await getProjectDetailApi({
    pid: route.query.pid as string,
  });
  await gridApi.grid.loadData(projectDetail.value.goodsList);
  updateFootEvent();
};
getDetail();
</script>

<template>
  <Page :title="projectDetail.name" auto-content-height show-back>
    <template #headerFooter>
      <Tabs>
        <TabPane key="info" :tab="$t('order.project.projectInfo')" />
        <TabPane key="overview" :tab="$t('order.project.projectOverview')" />
      </Tabs>
    </template>
    <Card :title="$t('order.project.baseInfo')">
      <DynamicDescriptions :data="projectDetail" :schema="baseFormSchema" />
    </Card>
    <Card class="mt-4" :title="$t('order.project.nodeInfo')">
      <DynamicDescriptions :data="projectDetail" :schema="nodeFormSchema">
        <template #nodeInfo="{ data }">
          <StatusTag v-for="item in data" :key="item">
            {{ dictStore.formatter(item, 'NODE_TYPE') }}
          </StatusTag>
        </template>
      </DynamicDescriptions>
    </Card>
    <Card class="mt-4" :title="$t('order.project.businessInfo')">
      <DynamicDescriptions :data="projectDetail" :schema="businessFormSchema" />
    </Card>
    <Card class="mt-4" :title="$t('order.project.financialInfo')">
      <DynamicDescriptions :data="projectDetail" :schema="financeFormSchema" />
    </Card>
    <Card class="mt-4" :title="$t('order.project.goodsInfo')">
      <Grid />
    </Card>
  </Page>
</template>

<style></style>
