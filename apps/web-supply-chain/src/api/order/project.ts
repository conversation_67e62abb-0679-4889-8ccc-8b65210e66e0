import { requestClient } from '#/api/request';

export interface GoodsInfo {
  amountTax: string;
  goodsName: string;
  models: '规格A' | '规格B' | '规格C' | '规格D';
  num: number;
  remark: string;
  taxRate: 6 | 9 | 13;
  unitPriceTax: number;
}

export interface ProjectInfo {
  pid: string;
  code: string;
  name: string;
  auditStatus: '00' | '10' | '20';
  remark: string;
  reason: string;
  status: '00' | '10' | '20' | '30' | '40';
  mode: '10' | '20' | '30';
  deliveryMethod: '01' | '02' | '03';
  tradeExecutionEnterpriseName: string;
  upEnterpriseName: string;
  downEnterpriseName: string;
  fundingPartyName: string;
  guarantorName: string;
  projectType: '类型A' | '类型B' | '类型C';
  businessDate: string;
  createDepartment: string;
  createBy: string;
  createTime: string;
  planStartDate: string;
  planCompletionDate: string;
  planDays: number;
  planAmount: number;
  projectAddressName: string;
  projectAddressDetail: string;
  nodeInfo: string[];
  goodsList: GoodsInfo[];
}

export async function getProjectPageListApi(params: { page: string }) {
  return requestClient.get('/order/project/page-list', { params });
}
export async function getProjectListApi(params: { page: string }) {
  return requestClient.get('/order/project/list', { params });
}
export async function getProjectDetailApi(params: { pid: string }) {
  return requestClient.get('/order/project/detail', { params });
}
