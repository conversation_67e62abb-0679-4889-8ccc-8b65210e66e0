export default eventHandler(async () => {
  const listData = [
    {
      pid: 'wlwjg_temp',
      code: 'wlwjg_temp',
      name: '物联网监管系统',
      iconType: '1',
      icon: 'ant-design:android-filled',
      appType: 'PLATFORM',
      url: 'http://supervisor.lr.demo2.fero.com.cn?demoLogin=eyJhY2NvdW50IjoiaHJkZW1vMyIsInBhc3N3b3JkIjoiMzQwZTA5Y2Q5MDk2ZjdlMSIsImhpcmVMaW5rSWQiOjUwM30=',
      topFlag: 0,
      enable: 1,
      createTime: 1_686_319_025_000,
      createBy: '3I0T2S0000000',
      updateTime: 1_703_460_281_000,
      updateBy: '3I0T2S0000000',
      version: 1,
      sort: 0,
    },
    {
      pid: 'policy',
      code: 'policy',
      name: '保险经纪业务',
      iconType: '1',
      icon: 'icon-jiaoyiguanlipingtai-o',
      appType: 'PLATFORM',
      url: 'http://policy.platform.ff360.com.cn',
      topFlag: 1,
      enable: 1,
      createTime: 1_731_470_461_000,
      createBy: '3I0T2S0000000',
      updateTime: 1_731_564_512_000,
      updateBy: '4T01KB0000000',
      version: 2,
      sort: 2,
    },
  ];
  return useResponseSuccess(listData);
});
