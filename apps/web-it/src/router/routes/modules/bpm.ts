import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/bpm',
    name: 'bpm',
    meta: {
      title: '工作流程',
      // hideInMenu: true,
    },
    children: [
      {
        path: 'manager',
        name: 'BpmTask',
        meta: {
          title: '流程管理',
          icon: 'fa:dedent',
        },
        children: [
          {
            path: 'category',
            name: 'BpmCategory',
            component: () => import('#/views/bpm/category/index.vue'),
            meta: {
              title: '流程分类',
              icon: 'fa:object-ungroup',
            },
          },
          {
            path: 'category',
            name: 'BpmCategory',
            component: () => import('#/views/bpm/category/index.vue'),
            meta: {
              title: '流程分类',
              icon: 'fa:object-ungroup',
            },
          },
        ],
      },
    ],
  },
];

export default routes;
