import { h } from 'vue';

import { StatusTag } from '@vben/base-ui';
import { $t } from '@vben/locales';
import { setupVbenVxeTable, useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';
import { formatDateMinute } from '@vben/utils';

import { Button, Image } from 'ant-design-vue';

import { useVbenForm } from './form';

const dictStore = useDictStore();

setupVbenVxeTable({
  configVxeTable: (vxeUI) => {
    vxeUI.setConfig({
      grid: {
        align: 'left',
        border: false,
        columnConfig: {
          minWidth: 'auto',
          resizable: true,
        },
        minHeight: 176,
        formConfig: {
          // 全局禁用vxe-table的表单配置，使用formOptions
          enabled: false,
        },
        proxyConfig: {
          autoLoad: true,
          response: {
            result: 'records',
            total: 'total',
          },
          showActiveMsg: true,
          showResponseMsg: false,
        },
        toolbarConfig: {
          custom: true,
          refresh: true,
          resizable: true,
          zoom: true,
        },
        round: true,
        showOverflow: true,
        size: 'small',
      },
    });

    // 表格配置项可以用 cellRender: { name: 'CellImage' },
    vxeUI.renderer.add('CellImage', {
      renderTableDefault(_renderOpts, params) {
        const { column, row } = params;
        return h(Image, { src: row[column.field] });
      },
    });

    // 表格配置项可以用 cellRender: { name: 'CellLink' },
    vxeUI.renderer.add('CellLink', {
      renderTableDefault(renderOpts) {
        const { props } = renderOpts;
        return h(Button, { size: 'small', type: 'link' }, { default: () => props?.text });
      },
    });
    vxeUI.renderer.add('CellStatus', {
      renderTableDefault(renderOpts, params) {
        const { props } = renderOpts;
        const { $grid, row, column } = params;
        let tooltipProps = {};
        if (props?.setTooltipShow && props?.setTooltipShow(row[column.field], params)) {
          tooltipProps = {
            title: row[props?.tooltipTitleKey] || props?.tooltipTitle,
            ...props?.tooltipProps,
          };
        }
        return h(StatusTag, {
          code: props?.code,
          value: row[column.field],
          tooltipProps,
        });
      },
    });

    // 这里可以自行扩展 vxe-table 的全局配置，比如自定义格式化
    vxeUI.formats.add('formatStatus', {
      tableCellFormatMethod({ cellValue }, code: string) {
        return dictStore.formatter(cellValue, code) as string;
      },
    });
    // vxeUI.formats.add('formatTime', {
    //   cellFormatMethod({ cellValue }, emptyString: string) {
    //     return formatDateTime(cellValue, emptyString);
    //   },
    // });
    vxeUI.formats.add('formatDateMinute', {
      tableCellFormatMethod({ cellValue }) {
        return formatDateMinute(cellValue);
      },
    });
    vxeUI.formats.add('formatBoolean', {
      tableCellFormatMethod({ cellValue }, labelFormat: { false: string; true: string }) {
        return cellValue === 1 ? (labelFormat?.true ?? $t('base.yes')) : (labelFormat?.false ?? $t('base.no'));
      },
    });
  },
  useVbenForm,
});

export { useVbenVxeGrid };

export type * from '@vben/plugins/vxe-table';
