<script setup lang="ts">
import type { ContractInfo, CreditContract, signInfo } from '#/api';

import { ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { defaultsDeep } from '@vben/utils';

import { Col as ACol, Row as ARow, Button, Card, QRCode, Space, TypographyLink } from 'ant-design-vue';

import {} from '#/api';

// const { getDictList } = useDictStore();

const contractInfo = ref<ContractInfo>({
  contractCode: '',
  contractName: '',
  signStatus: '',
  signMethod: '',
  partyA: '',
  partyB: '',
  signStartTime: '',
  signEndTime: '',
  signInfoList: [],
});

const init = (data: CreditContract) => {
  contractInfo.value = defaultsDeep(data);
};
const formRef = ref();

const tableData = ref<signInfo[]>([
  {
    signParty: '甲方',
    signEnterprise: '上海贸易有限公司',
    signer: '张三',
    signLink: 'https://example.com/sign/123',
    signProgress: '100%',
    signDate: '2023-06-15 10:20',
  },
  {
    signParty: '乙方',
    signEnterprise: '北京科技有限公司',
    signer: '李四',
    signLink: 'https://example.com/sign/456',
    signProgress: '50%',
    signDate: '2023-06-18 14:30',
  },
  {
    signParty: '丙方',
    signEnterprise: '广州信息技术有限公司',
    signer: '王五',
    signLink: 'https://example.com/sign/789',
    signProgress: '0%',
    signDate: '-',
  },
]);
// const roleList = ref([]);
// const { systemConfig } = useSystemConfigStore();
const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="详情" @register="registerPopup">
    <Form
      ref="formRef"
      :colon="false"
      :model="contractInfo"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <BasicCaption content="合同信息" />
      <a-descriptions>
        <a-descriptions-item label="合同名称">{{ contractInfo.contractName }}</a-descriptions-item>
        <a-descriptions-item label="合同编号">{{ contractInfo.contractCode }}</a-descriptions-item>
        <a-descriptions-item label="发起签署日期">{{ contractInfo.signStartTime }}</a-descriptions-item>
        <a-descriptions-item label="完成签署日期">{{ contractInfo.signEndTime }}</a-descriptions-item>
        <a-descriptions-item label="签署方式">{{ contractInfo.signMethod }}</a-descriptions-item>
        <a-descriptions-item label="签署状态">{{ contractInfo.signStatus }}</a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="签署信息" />
      <!-- <Grid>
                <template #signLink="{ row }">

                </template>
                <template #operate>
                    <Button type="link">预览</Button>
                </template>
            </Grid> -->
      <vxe-table :data="tableData">
        <vxe-column type="seq" width="70" />
        <vxe-column field="signParty" title="签署方" />
        <vxe-column field="signEnterprise" title="签署企业" />
        <vxe-column field="signer" title="签署人" />
        <vxe-column field="signLink" title="签署链接">
          <template #default="{ row }">
            <ARow>
              <!-- 左侧二维码 -->
              <ACol :span="8">
                <Card
                  :bordered="false"
                  style="display: flex; align-items: center; justify-content: center; height: 120px; text-align: center"
                >
                  <QRCode :value="row.signLink" :size="120" color="#000" bg-color="#fff" />
                </Card>
              </ACol>
              <!-- 右侧内容 -->
              <ACol :span="16">
                <Space direction="vertical" size="small" style="width: 100%">
                  <!-- 上方链接 -->
                  <TypographyLink copyable> https://example.com/sign/123456 </TypographyLink>
                  <!-- 下方文字 -->
                  <TypographyLink type="secondary" style="font-size: 14px">
                    请使用手机扫描二维码完成签署，链接有效期为7天
                  </TypographyLink>
                </Space>
              </ACol>
            </ARow>
          </template>
        </vxe-column>
        <vxe-column field="signProgress" title="签署进度" />
        <vxe-column field="signDate" title="签署日期" />
      </vxe-table>
      <BasicCaption content="合同预览">
        <template #action>
          <Button>下载待签pdf文件</Button>
        </template>
      </BasicCaption>

      <BasicCaption content="合同关键字段" />
      <a-descriptions>
        <a-descriptions-item label="UserName">Zhou Maomao</a-descriptions-item>
        <a-descriptions-item label="Telephone">1810000000</a-descriptions-item>
        <a-descriptions-item label="Live">Hangzhou, Zhejiang</a-descriptions-item>
        <a-descriptions-item label="Remark">empty</a-descriptions-item>
        <a-descriptions-item label="Address">
          No. 18, Wantang Road, Xihu District, Hangzhou, Zhejiang, China
        </a-descriptions-item>
      </a-descriptions>
    </Form>
  </BasicPopup>
</template>

<style scoped>
/* 强制表格行高自适应 */
:deep(.vxe-body--row) {
  height: auto !important;
}

:deep(.vxe-body--column) {
  height: auto !important;
}
</style>
