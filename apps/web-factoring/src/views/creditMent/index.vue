<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Button, Space, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import Form from './components/creditMentForm.vue';

interface RowType {
  category: string;
  color: string;
  id: string;
  price: string;
  productName: string;
  releaseDate: string;
}

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入综合授信编号',
      },
      fieldName: 'category',
      label: '综合授信编号',
    },
    {
      component: 'Input',
      fieldName: 'productName',
      componentProps: {
        placeholder: '请输入授信企业',
      },
      label: '授信企业',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [],
        placeholder: '请选择授信对象',
      },
      fieldName: 'vo',
      label: '授信对象',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [],
        placeholder: '请选择客户类别',
      },
      fieldName: 'kehu',
      label: '客户类别',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [],
        placeholder: '请选择操作状态',
      },
      fieldName: 'caozuo',
      label: '操作状态',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [],
        placeholder: '请选择审批状态',
      },
      fieldName: 'shenpi',
      label: '审批状态',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [],
        placeholder: '请选择合同状态',
      },
      fieldName: 'hetong',
      label: '合同状态',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [],
        placeholder: '请选择授信状态',
      },
      fieldName: 'shouxin',
      label: '授信状态',
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
  },
  // 按下回车时是否提交表单
  submitOnEnter: true,
};

const gridOptions: VxeGridProps<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  height: 'auto',
  columns: [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'code', title: '综合授信编号' },
    { field: 'name', title: '授信企业' },
    { field: 'silCode', title: '统一社会信用代码' },
    { field: 'amx', title: '授信申请金额（元）' },
    { field: 'amsCount', formatter: 'formatDateTime', title: '授信批复金额（元）' },
    { field: 'status', title: '操作状态' },
    { field: 'shenpi', title: '审批状态' },
    { field: 'htStatus', formatter: 'formatDateTime', title: '合同状态' },
    { field: 'sxStatus', title: '授信状态' },
    { field: 'shouliDate', formatter: 'formatDateTime', title: '受理日期' },
    { field: 'pifuDate', formatter: 'formatDateTime', title: '批复日期' },
    { slots: { default: 'action' }, title: '操作' },
  ],
  keepSource: true,
  pagerConfig: {},
  //   proxyConfig: {
  //     ajax: {
  //       query: async ({ page }, formValues) => {
  //         message.success(`Query params: ${JSON.stringify(formValues)}`);
  //         return await getExampleTableApi({
  //           page: page.currentPage,
  //           pageSize: page.pageSize,
  //           ...formValues,
  //         });
  //       },
  //     },
  //   },
  toolbarConfig: {
    // 是否显示搜索表单控制按钮
    // @ts-ignore 正式环境时有完整的类型声明
    // search: true,
  },
};
const modalTitle = ref($t('base.add'));
const edit = () => {
  modalTitle.value = $t('base.edit');
  // modalApi.setData(row).open();
};

const addRole = () => {
  openFormPopup(true, { organizeIds: '2321' });
};
const del = () => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      // await deleteGroupApi(row.id);
      // message.success($t('base.resSuccess'));
      // await gridApi.reload();
    },
  });
};
const editSuccess = () => {};
const handleDetail = () => {
  modalTitle.value = $t('base.detail');
  // modalApi.setData(row).open();
};
const goSign = () => {};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [Grid] = useVbenVxeGrid({ formOptions, gridOptions });
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="addRole">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action>
        <Space>
          <TypographyLink @click="edit()">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del()">
            {{ $t('base.del') }}
          </TypographyLink>
          <TypographyLink @click="handleDetail()">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink @click="goSign()">
            {{ $t('base.goSign') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Form @register="registerForm" @ok="editSuccess" />
  </Page>
</template>
