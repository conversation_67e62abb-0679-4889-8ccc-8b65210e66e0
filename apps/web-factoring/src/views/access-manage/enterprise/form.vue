<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { EnterpriseInfo } from '#/api';

import { computed, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import {
  Button,
  Col,
  DatePicker,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  RangePicker,
  Row,
  Select,
  Textarea,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addEnterpriseApi, editEnterpriseApi, infoEnterpriseApi } from '#/api';

const emit = defineEmits(['register', 'ok']);
const { getDictList } = useDictStore();
// 根据接口定义初始化信息
const defaultForm: Partial<EnterpriseInfo> = {
  companyName: '',
  creditCode: '',
  industry: '',
  legalRepresentative: '',
  organCode: '',
  taxpayerNumber: '',
  busRegistrationNumber: '',
  registrationStatus: '',
  establishDate: undefined,
  taxpayerQualification: '',
  businessTermStart: undefined,
  businessTermEnd: undefined,
  principalApprovalTime: undefined,
  isIpo: '',
  companyType: '',
  personnelPhone: '',
  personnelScale: '',
  registeredCapital: 0,
  companyContactEmail: '',
  companyOfficialWebsite: '',
  actualCapital: 0,
  registerOrgan: '',
  financialAccountingNumber: '',
  auxiliaryAccountingCode: '',
  registeredAddress: {},
  registeredAddressAll: '',
  businessAddress: {},
  businessAddressAll: '',
  businessScope: '',
  remarks: '',
  businessRoles: '',
  status: '',
  enableStatus: '', // 修复：添加逗号
  invoiceList: [],
};

const enterpriseInfo = ref<Partial<EnterpriseInfo>>(defaultsDeep(defaultForm));
const colSpan = { md: 12, sm: 24 };
// 修改4：更新验证规则以匹配Enterprise接口字段
const rules: Record<string, Rule[]> = {
  companyName: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
  creditCode: [{ required: true, message: '请输入统一社会信用代码', trigger: 'blur' }],
  industry: [{ required: false, message: '请选择所属行业', trigger: 'change' }],
  legalRepresentative: [{ required: false, message: '请输入法定代表人', trigger: 'blur' }],
  organCode: [{ required: false, message: '请输入组织机构代码', trigger: 'blur' }],
  taxpayerNumber: [{ required: false, message: '请输入纳税人识别号', trigger: 'blur' }],
  busRegistrationNumber: [{ required: false, message: '请输入工商注册号', trigger: 'blur' }],
  registrationStatus: [{ required: false, message: '请输入登记状态', trigger: 'blur' }],
  establishDate: [{ required: false, message: '请选择成立日期', trigger: 'change' }],
  taxpayerQualification: [{ required: false, message: '请输入纳税人资质', trigger: 'blur' }],
  businessTermStart: [{ required: false, message: '请选择营业期限开始', trigger: 'change' }],
  businessTermEnd: [{ required: false, message: '请选择营业期限结束', trigger: 'change' }],
  principalApprovalTime: [{ required: false, message: '请选择核准日期', trigger: 'change' }],
  companyType: [{ required: false, message: '请输入企业类型', trigger: 'blur' }],
  personnelPhone: [{ required: false, message: '请输入企业联系电话', trigger: 'blur' }],
  personnelScale: [{ required: false, message: '请输入人员规模', trigger: 'blur' }],
  registeredCapital: [{ required: false, message: '请输入注册资本', trigger: 'blur' }],
  companyContactEmail: [{ required: false, message: '请输入企业联系邮箱', trigger: 'blur' }],
  companyOfficialWebsite: [{ required: false, message: '请输入企业官网', trigger: 'blur' }],
  actualCapital: [{ required: false, message: '请输入实缴资本', trigger: 'blur' }],
  registerOrgan: [{ required: false, message: '请输入登记机关', trigger: 'blur' }],
  financialAccountingNumber: [{ required: false, message: '请输入财务核算账簿编号', trigger: 'blur' }],
  auxiliaryAccountingCode: [{ required: false, message: '请输入辅助核算编码', trigger: 'blur' }],
  businessScope: [{ required: false, message: '请输入经营范围', trigger: 'blur' }],
  remarks: [{ required: false, message: '请输入备注', trigger: 'blur' }],
};
const title = computed(() => {
  return enterpriseInfo.value.id ? '编辑企业' : '新增企业';
});
const init = async (data: EnterpriseInfo) => {
  if (data.id) {
    enterpriseInfo.value = await infoEnterpriseApi({ id: data.id as string });
  }
};
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', dragSort: true },
    { field: 'titleName', title: '抬头名称', editRender: {}, slots: { edit: 'edit_title_name' } },
    { field: 'taxpayerNumber', title: '纳税人识别号', editRender: {}, slots: { edit: 'edit_taxpayer_number' } },
    { field: 'depositBank', title: '开户行', editRender: {}, slots: { edit: 'edit_deposit_bank' } },
    { field: 'bankAccount', title: '银行账号', editRender: {}, slots: { edit: 'edit_bank_account' } },
    { field: 'depositAddress', title: '开户地址', editRender: {}, slots: { edit: 'edit_deposit_address' } },
  ],
  data: enterpriseInfo.value.invoiceList,
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [Grid] = useVbenVxeGrid({
  gridOptions,
});
const addInvoice = () => {};
const formRef = ref();
const save = async () => {
  await formRef.value.validate();
  changeOkLoading(true);
  let api = addEnterpriseApi;
  if (enterpriseInfo.value.id) {
    api = editEnterpriseApi;
  }
  try {
    const res = await api(enterpriseInfo.value as EnterpriseInfo);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const labelCol = { style: { width: '150px' } };
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="enterpriseInfo"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基础信息 -->
      <BasicCaption content="基础信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="企业名称" name="companyName">
            <Input v-model:value="enterpriseInfo.companyName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="统一社会信用代码" name="creditCode">
            <Input v-model:value="enterpriseInfo.creditCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="所属行业" name="industry">
            <Select v-model:value="enterpriseInfo.industry" :options="getDictList('industry')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="法定代表" name="legalRepresentative">
            <Input v-model:value="enterpriseInfo.legalRepresentative" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="组织机构代码" name="organCode">
            <Input v-model:value="enterpriseInfo.organCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="纳税人识别号" name="taxpayerNumber">
            <Input v-model:value="enterpriseInfo.taxpayerNumber" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="工商注册号" name="busRegistrationNumber">
            <Input v-model:value="enterpriseInfo.busRegistrationNumber" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="登记状态" name="registrationStatus">
            <Select v-model:value="enterpriseInfo.registrationStatus" :options="getDictList('registrationStatus')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="成立日期" name="establishDate">
            <DatePicker v-model:value="enterpriseInfo.establishDate" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="纳税人资质" name="taxpayerQualification">
            <Input v-model:value="enterpriseInfo.taxpayerQualification" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="营业期限" name="businessTerm">
            <RangePicker v-model:value="enterpriseInfo.businessTerm" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="核准日期" name="principalApprovalTime">
            <DatePicker v-model:value="enterpriseInfo.principalApprovalTime" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否上市公司" name="isIpo">
            <Select v-model:value="enterpriseInfo.isIpo" :options="getDictList('isIpo')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="企业类型" name="companyType">
            <Select v-model:value="enterpriseInfo.companyType" :options="getDictList('companyType')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="企业联系电话" name="personnelPhone">
            <Input v-model:value="enterpriseInfo.personnelPhone" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="人员规模" name="personnelScale">
            <Input v-model:value="enterpriseInfo.personnelScale" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="注册资本（万元）" name="registeredCapital">
            <InputNumber v-model:value="enterpriseInfo.registeredCapital" :controls="false" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="企业联系邮箱" name="companyContactEmail">
            <Input v-model:value="enterpriseInfo.companyContactEmail" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="企业官网" name="companyOfficialWebsite">
            <Input v-model:value="enterpriseInfo.companyOfficialWebsite" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="实缴资本（万元）" name="actualCapital">
            <InputNumber v-model:value="enterpriseInfo.actualCapital" :controls="false" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="登记机关" name="registerOrgan">
            <Input v-model:value="enterpriseInfo.registerOrgan" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="财务核算账簿编号" name="financialAccountingNumber">
            <Input v-model:value="enterpriseInfo.financialAccountingNumber" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="辅助核算编码" name="auxiliaryAccountingCode">
            <Input v-model:value="enterpriseInfo.auxiliaryAccountingCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="注册地址" name="registeredAddressAll">
            <Input v-model:value="enterpriseInfo.registeredAddressAll" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="经营地址" name="businessAddressAll">
            <Input v-model:value="enterpriseInfo.businessAddressAll" />
          </FormItem>
        </Col>
        <Col :span="24">
          <FormItem label="经营范围" name="businessScope">
            <Textarea v-model:value="enterpriseInfo.businessScope" :rows="4" />
          </FormItem>
        </Col>
        <Col :span="24">
          <FormItem label="备注" name="remarks">
            <Textarea v-model:value="enterpriseInfo.remarks" :rows="4" />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="开票信息" />
      <Grid>
        <template #toolbar-actions>
          <Button class="mr-2" type="primary" @click="addInvoice">增行</Button>
        </template>
        <template #edit_title_name="{ row }">
          <Input v-model:value="row.titleName" placeholder="请输入抬头名称" />
        </template>
        <template #edit_taxpayer_number="{ row }">
          <Input v-model:value="row.taxpayerNumber" placeholder="请输入纳税人识别号" />
        </template>
        <template #edit_deposit_bank="{ row }">
          <Input v-model:value="row.depositBank" placeholder="请输入开户行" />
        </template>
        <template #edit_bank_account="{ row }">
          <Input v-model:value="row.bankAccount" placeholder="请输入银行账号" />
        </template>
        <template #edit_deposit_address="{ row }">
          <Input v-model:value="row.depositAddress" placeholder="请输入开户地址" />
        </template>
      </Grid>
    </Form>
  </BasicPopup>
</template>

<style scoped></style>
