import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

// 性别
export interface InvoiceInfo extends BaseDataParams {
  // 抬头名称
  titleName: string;
  // 纳税人识别号
  taxpayerNumber: string;
  // 开户行
  depositBank: string;
  // 银行账号
  bankAccount: string;
  // 开户地址
  depositAddress?: string;
  // 是否默认（0=否，1=是）
  isDefault: string;
  // 状态
  status: string;
}

export interface EnterpriseInfo extends BaseDataParams {
  // 企业名称
  companyName: string;
  // 统一社会信用代码
  creditCode: string;
  // 所属行业
  industry?: string;
  // 法定代表
  legalRepresentative?: string;
  // 组织结构代码
  organCode?: string;
  // 纳税人识别号
  taxpayerNumber?: string;
  // 工商注册号
  busRegistrationNumber?: string;
  // 登记状态
  registrationStatus?: string;
  // 成立日期
  establishDate?: Date;
  // 纳税人资质
  taxpayerQualification?: string;
  // 营业期限开始
  businessTermStart?: Date;
  // 营业期限结束
  businessTermEnd?: Date;
  // 核准日期
  principalApprovalTime?: Date;
  // 是否上市公司
  isIpo?: boolean;
  // 企业类型
  companyType?: string;
  // 企业联系电话
  personnelPhone?: string;
  // 人员规模
  personnelScale?: string;
  // 注册资本（万元）
  registeredCapital?: number;
  // 企业联系邮箱
  companyContactEmail?: string;
  // 企业官网
  companyOfficialWebsite?: string;
  // 实缴资本（万元）
  actualCapital?: number;
  // 登记机关
  registerOrgan?: string;
  // 财务核算账簿编号
  financialAccountingNumber?: string;
  // 辅助核算编码
  auxiliaryAccountingCode?: string;
  // 注册地址json
  registeredAddress?: object;
  // 注册地址全称
  registeredAddressAll?: string;
  // 经营地址json
  businessAddress?: object;
  // 经营地址全称
  businessAddressAll?: string;
  // 经营范围
  businessScope?: string;
  // 备注
  remarks?: string;
  // 业务角色（逗号分割）
  businessRoles?: string;
  // 操作状态
  status?: string;
  // 启用状态
  enableStatus?: string;
  // 开票信息
  invoiceList?: InvoiceInfo[];
}

export async function getEnterprisePageListApi(params: PageListParams) {
  return requestClient.get<EnterpriseInfo[]>('/upms/user/page', { params });
}
export async function addEnterpriseApi(data: EnterpriseInfo) {
  return requestClient.post<EnterpriseInfo>('/upms/user/add', data);
}
export async function editEnterpriseApi(data: EnterpriseInfo) {
  return requestClient.post<EnterpriseInfo>('/upms/user/edit', data);
}
export async function infoEnterpriseApi(data: EnterpriseInfo) {
  return requestClient.post<EnterpriseInfo>('/upms/user/info', data);
}
export async function delEnterpriseApi(id: string) {
  return requestClient.post('/upms/user/delete', {}, { params: { id } });
}
