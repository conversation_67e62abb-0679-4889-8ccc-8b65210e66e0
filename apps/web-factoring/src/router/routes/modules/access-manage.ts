import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: '',
      title: $t('page.access-manage.title'),
    },
    name: 'AccessManage',
    path: '/access-manage',
    children: [
      {
        name: 'AccessManageEnterprise',
        path: '/access-manage/enterprise',
        component: () => import('#/views/access-manage/enterprise/index.vue'),
        meta: {
          icon: '',
          title: $t('page.access-manage.enterprise'),
        },
      },
    ],
  },
];

export default routes;
