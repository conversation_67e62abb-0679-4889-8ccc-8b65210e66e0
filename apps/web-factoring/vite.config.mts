import { defineConfig } from '@vben/vite-config';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      server: {
        proxy: {
          '/api/ai': {
            changeOrigin: true,
            // mock代理目标地址
            target: 'http://*************',
            ws: true,
          },
          '/api/upms': {
            changeOrigin: true,
            // mock代理目标地址
            target: 'http://*************',
            ws: true,
          },
          // eslint-disable-next-line perfectionist/sort-objects
          '/api': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api/, ''),
            // mock代理目标地址
            target: 'http://localhost:5320/api',
            ws: true,
          },
        },
      },
    },
  };
});
